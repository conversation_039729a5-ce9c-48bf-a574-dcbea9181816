// 历史时期数据类型定义

export interface HistoryPeriod {
  id: string;
  name: string;
  startYear: number;
  endYear: number;
  dynasty?: string; // 朝代名称（中国历史专用）
  region: 'china' | 'world';
  color: string; // 时间轴上的颜色
  description: string;
  keyEvents: HistoryEvent[];
  culturalFeatures: string[];
  politicalSystem: string;
  economicFeatures: string[];
  notablePersons: NotablePerson[];
  achievements: string[];
  capital?: string; // 首都或重要城市
  territory?: string; // 疆域描述
}

export interface HistoryEvent {
  id: string;
  year: number;
  title: string;
  description: string;
  significance: string;
  type: 'political' | 'military' | 'cultural' | 'economic' | 'social' | 'technological';
  impact: 'local' | 'regional' | 'global';
}

export interface NotablePerson {
  id: string;
  name: string;
  birthYear?: number;
  deathYear?: number;
  role: string; // 身份/职业
  achievements: string[];
  significance: string;
}

export interface TimelineData {
  chinaTimeline: HistoryPeriod[];
  worldTimeline: HistoryPeriod[];
  timeRange: {
    startYear: number;
    endYear: number;
  };
}

export interface TimelineViewport {
  startYear: number;
  endYear: number;
  scale: number; // 缩放比例
  centerYear: number; // 当前视图中心年份
}

export interface SelectedPeriod {
  china?: HistoryPeriod;
  world?: HistoryPeriod;
  year: number;
}

// 时间轴配置
export interface TimelineConfig {
  height: number;
  minYear: number;
  maxYear: number;
  pixelsPerYear: number;
  majorTickInterval: number; // 主要刻度间隔（年）
  minorTickInterval: number; // 次要刻度间隔（年）
}

// 历史对比数据
export interface HistoryComparison {
  year: number;
  chinaContext: {
    period: HistoryPeriod;
    events: HistoryEvent[];
    culturalContext: string;
  };
  worldContext: {
    periods: HistoryPeriod[];
    events: HistoryEvent[];
    globalTrends: string[];
  };
  connections: string[]; // 中外历史的联系
}
