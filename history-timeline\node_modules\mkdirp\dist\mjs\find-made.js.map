{"version": 3, "file": "find-made.js", "sourceRoot": "", "sources": ["../../src/find-made.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,MAAM,CAAA;AAG9B,MAAM,CAAC,MAAM,QAAQ,GAAG,KAAK,EAC3B,IAA2B,EAC3B,MAAc,EACd,IAAa,EACgB,EAAE;IAC/B,+DAA+D;IAC/D,IAAI,IAAI,KAAK,MAAM,EAAE;QACnB,OAAM;KACP;IAED,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAChC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,kBAAkB;IAC/D,AAD6C,kBAAkB;IAC/D,EAAE,CAAC,EAAE;QACH,MAAM,GAAG,GAAG,EAA2B,CAAA;QACvC,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ;YACjC,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;YACzC,CAAC,CAAC,SAAS,CAAA;IACf,CAAC,CACF,CAAA;AACH,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,YAAY,GAAG,CAC1B,IAA2B,EAC3B,MAAc,EACd,IAAa,EACO,EAAE;IACtB,IAAI,IAAI,KAAK,MAAM,EAAE;QACnB,OAAO,SAAS,CAAA;KACjB;IAED,IAAI;QACF,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAA;KAC9D;IAAC,OAAO,EAAE,EAAE;QACX,MAAM,GAAG,GAAG,EAA2B,CAAA;QACvC,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ;YACjC,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;YAC7C,CAAC,CAAC,SAAS,CAAA;KACd;AACH,CAAC,CAAA", "sourcesContent": ["import { dirname } from 'path'\nimport { MkdirpOptionsResolved } from './opts-arg.js'\n\nexport const findMade = async (\n  opts: MkdirpOptionsResolved,\n  parent: string,\n  path?: string\n): Promise<undefined | string> => {\n  // we never want the 'made' return value to be a root directory\n  if (path === parent) {\n    return\n  }\n\n  return opts.statAsync(parent).then(\n    st => (st.isDirectory() ? path : undefined), // will fail later\n    er => {\n      const fer = er as NodeJS.ErrnoException\n      return fer && fer.code === 'ENOENT'\n        ? findMade(opts, dirname(parent), parent)\n        : undefined\n    }\n  )\n}\n\nexport const findMadeSync = (\n  opts: MkdirpOptionsResolved,\n  parent: string,\n  path?: string\n): undefined | string => {\n  if (path === parent) {\n    return undefined\n  }\n\n  try {\n    return opts.statSync(parent).isDirectory() ? path : undefined\n  } catch (er) {\n    const fer = er as NodeJS.ErrnoException\n    return fer && fer.code === 'ENOENT'\n      ? findMadeSync(opts, dirname(parent), parent)\n      : undefined\n  }\n}\n"]}