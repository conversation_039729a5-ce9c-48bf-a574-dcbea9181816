import React, { Component, ErrorInfo, ReactNode } from 'react';
import { motion } from 'framer-motion';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('<PERSON><PERSON>rBounda<PERSON> caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });
  }

  handleReload = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-ancient-50 via-ancient-100 to-sage-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="max-w-2xl w-full bg-white rounded-xl shadow-2xl overflow-hidden"
          >
            {/* 错误图标区域 */}
            <div className="bg-gradient-to-r from-red-500 to-red-600 text-white p-8 text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="inline-flex items-center justify-center w-20 h-20 bg-white bg-opacity-20 rounded-full mb-4"
              >
                <AlertTriangle className="w-10 h-10" />
              </motion.div>
              <h1 className="text-2xl font-bold font-chinese">
                哎呀！出现了一些问题
              </h1>
              <p className="text-red-100 mt-2">
                历史时间轴遇到了意外错误
              </p>
            </div>

            {/* 错误信息区域 */}
            <div className="p-8">
              <div className="mb-6">
                <h2 className="text-lg font-semibold text-ancient-800 font-chinese mb-3">
                  发生了什么？
                </h2>
                <p className="text-ancient-600 font-chinese leading-relaxed">
                  应用程序遇到了一个意外错误。这可能是由于网络问题、浏览器兼容性或者程序bug导致的。
                  请尝试以下解决方案：
                </p>
              </div>

              {/* 解决方案 */}
              <div className="space-y-4 mb-8">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="flex items-start space-x-3 p-4 bg-ancient-50 rounded-lg"
                >
                  <div className="w-6 h-6 bg-ancient-200 rounded-full flex items-center justify-center text-sm font-bold text-ancient-700 mt-0.5">
                    1
                  </div>
                  <div>
                    <h3 className="font-semibold text-ancient-800 font-chinese">刷新页面</h3>
                    <p className="text-sm text-ancient-600 font-chinese">
                      大多数临时错误可以通过刷新页面解决
                    </p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  className="flex items-start space-x-3 p-4 bg-ancient-50 rounded-lg"
                >
                  <div className="w-6 h-6 bg-ancient-200 rounded-full flex items-center justify-center text-sm font-bold text-ancient-700 mt-0.5">
                    2
                  </div>
                  <div>
                    <h3 className="font-semibold text-ancient-800 font-chinese">检查网络连接</h3>
                    <p className="text-sm text-ancient-600 font-chinese">
                      确保您的网络连接正常
                    </p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  className="flex items-start space-x-3 p-4 bg-ancient-50 rounded-lg"
                >
                  <div className="w-6 h-6 bg-ancient-200 rounded-full flex items-center justify-center text-sm font-bold text-ancient-700 mt-0.5">
                    3
                  </div>
                  <div>
                    <h3 className="font-semibold text-ancient-800 font-chinese">更新浏览器</h3>
                    <p className="text-sm text-ancient-600 font-chinese">
                      使用最新版本的现代浏览器以获得最佳体验
                    </p>
                  </div>
                </motion.div>
              </div>

              {/* 操作按钮 */}
              <div className="flex flex-col sm:flex-row gap-4">
                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  onClick={this.handleReload}
                  className="flex-1 bg-imperial-600 text-white px-6 py-3 rounded-lg hover:bg-imperial-700 transition-colors flex items-center justify-center space-x-2 font-chinese"
                >
                  <RefreshCw className="w-5 h-5" />
                  <span>刷新页面</span>
                </motion.button>

                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 }}
                  onClick={this.handleReset}
                  className="flex-1 bg-ancient-600 text-white px-6 py-3 rounded-lg hover:bg-ancient-700 transition-colors flex items-center justify-center space-x-2 font-chinese"
                >
                  <Home className="w-5 h-5" />
                  <span>重试</span>
                </motion.button>
              </div>

              {/* 技术信息（开发模式下显示） */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <motion.details
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                  className="mt-8 p-4 bg-gray-50 rounded-lg border"
                >
                  <summary className="cursor-pointer font-semibold text-gray-700 mb-2">
                    技术详情（开发模式）
                  </summary>
                  <div className="space-y-2">
                    <div>
                      <strong>错误信息：</strong>
                      <pre className="text-sm text-red-600 bg-red-50 p-2 rounded mt-1 overflow-auto">
                        {this.state.error.message}
                      </pre>
                    </div>
                    {this.state.error.stack && (
                      <div>
                        <strong>错误堆栈：</strong>
                        <pre className="text-xs text-gray-600 bg-gray-100 p-2 rounded mt-1 overflow-auto max-h-40">
                          {this.state.error.stack}
                        </pre>
                      </div>
                    )}
                  </div>
                </motion.details>
              )}

              {/* 联系信息 */}
              <div className="mt-8 p-4 bg-sage-50 rounded-lg border border-sage-200">
                <p className="text-sm text-sage-700 font-chinese text-center">
                  如果问题持续存在，这可能是一个程序错误。
                  <br />
                  感谢您的耐心，我们正在努力改进用户体验。
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      );
    }

    return this.props.children;
  }
}
