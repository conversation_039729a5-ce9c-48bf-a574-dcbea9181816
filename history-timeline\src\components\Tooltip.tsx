import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { HistoryPeriod } from '../types/history';
import { formatYear } from '../utils/timelineUtils';

interface TooltipProps {
  period: HistoryPeriod;
  children: React.ReactElement;
  delay?: number;
}

export const Tooltip: React.FC<TooltipProps> = ({ 
  period, 
  children, 
  delay = 500 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const timeoutRef = useRef<NodeJS.Timeout>();
  const triggerRef = useRef<HTMLDivElement>(null);

  const showTooltip = (e: React.MouseEvent) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      const rect = e.currentTarget.getBoundingClientRect();
      setPosition({
        x: rect.left + rect.width / 2,
        y: rect.top - 10
      });
      setIsVisible(true);
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        className="inline-block"
      >
        {children}
      </div>

      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 10 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="fixed z-50 pointer-events-none"
            style={{
              left: position.x,
              top: position.y,
              transform: 'translateX(-50%) translateY(-100%)'
            }}
          >
            <div className="bg-white rounded-lg shadow-xl border border-ancient-200 p-4 max-w-sm">
              {/* 箭头 */}
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full">
                <div className="w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-white"></div>
              </div>

              {/* 内容 */}
              <div className="space-y-3">
                {/* 标题 */}
                <div className="border-b border-ancient-100 pb-2">
                  <h3 className="font-bold text-ancient-800 font-chinese text-lg">
                    {period.name}
                  </h3>
                  <p className="text-sm text-ancient-600">
                    {formatYear(period.startYear)} - {formatYear(period.endYear)}
                  </p>
                </div>

                {/* 基本信息 */}
                <div className="space-y-2">
                  {period.dynasty && (
                    <div className="flex items-center text-sm">
                      <span className="text-ancient-500 w-16">朝代：</span>
                      <span className="text-ancient-700 font-chinese">{period.dynasty}</span>
                    </div>
                  )}
                  
                  {period.capital && (
                    <div className="flex items-center text-sm">
                      <span className="text-ancient-500 w-16">都城：</span>
                      <span className="text-ancient-700 font-chinese">{period.capital}</span>
                    </div>
                  )}

                  <div className="flex items-center text-sm">
                    <span className="text-ancient-500 w-16">制度：</span>
                    <span className="text-ancient-700 font-chinese">{period.politicalSystem}</span>
                  </div>
                </div>

                {/* 描述 */}
                <div>
                  <p className="text-sm text-ancient-700 font-chinese leading-relaxed">
                    {period.description.length > 100 
                      ? `${period.description.substring(0, 100)}...` 
                      : period.description
                    }
                  </p>
                </div>

                {/* 文化特色标签 */}
                {period.culturalFeatures.length > 0 && (
                  <div>
                    <p className="text-xs text-ancient-500 mb-2">文化特色：</p>
                    <div className="flex flex-wrap gap-1">
                      {period.culturalFeatures.slice(0, 3).map((feature, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-sage-100 text-sage-700 rounded text-xs font-chinese"
                        >
                          {feature}
                        </span>
                      ))}
                      {period.culturalFeatures.length > 3 && (
                        <span className="px-2 py-1 bg-ancient-100 text-ancient-600 rounded text-xs">
                          +{period.culturalFeatures.length - 3}
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {/* 重要事件 */}
                {period.keyEvents.length > 0 && (
                  <div>
                    <p className="text-xs text-ancient-500 mb-2">重要事件：</p>
                    <div className="space-y-1">
                      {period.keyEvents.slice(0, 2).map((event, index) => (
                        <div key={event.id} className="text-xs">
                          <span className="text-ancient-600">
                            {formatYear(event.year)}
                          </span>
                          <span className="text-ancient-700 font-chinese ml-2">
                            {event.title}
                          </span>
                        </div>
                      ))}
                      {period.keyEvents.length > 2 && (
                        <p className="text-xs text-ancient-500 italic">
                          还有 {period.keyEvents.length - 2} 个重要事件...
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {/* 提示 */}
                <div className="border-t border-ancient-100 pt-2">
                  <p className="text-xs text-ancient-500 italic text-center">
                    点击查看详细信息
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
