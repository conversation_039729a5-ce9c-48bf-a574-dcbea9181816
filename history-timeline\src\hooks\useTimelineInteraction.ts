import { useState, useCallback, useRef, useEffect } from 'react';
import { TimelineViewport } from '../types/history';
import { defaultTimelineConfig, panTimeline, zoomTimeline } from '../utils/timelineUtils';

interface UseTimelineInteractionProps {
  viewport: TimelineViewport;
  onViewportChange: (viewport: TimelineViewport) => void;
}

interface InteractionState {
  isDragging: boolean;
  isZooming: boolean;
  dragStart: { x: number; y: number; scrollLeft: number; scrollTop: number };
  lastTouchDistance: number;
}

export function useTimelineInteraction({ viewport, onViewportChange }: UseTimelineInteractionProps) {
  const [interactionState, setInteractionState] = useState<InteractionState>({
    isDragging: false,
    isZooming: false,
    dragStart: { x: 0, y: 0, scrollLeft: 0, scrollTop: 0 },
    lastTouchDistance: 0,
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number>();

  // 处理鼠标拖拽开始
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!containerRef.current) return;
    
    e.preventDefault();
    setInteractionState(prev => ({
      ...prev,
      isDragging: true,
      dragStart: {
        x: e.clientX,
        y: e.clientY,
        scrollLeft: containerRef.current!.scrollLeft,
        scrollTop: containerRef.current!.scrollTop,
      }
    }));
  }, []);

  // 处理鼠标移动
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!interactionState.isDragging || !containerRef.current) return;
    
    e.preventDefault();
    
    // 使用 requestAnimationFrame 优化性能
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    
    animationFrameRef.current = requestAnimationFrame(() => {
      if (!containerRef.current) return;
      
      const deltaX = e.clientX - interactionState.dragStart.x;
      const deltaY = e.clientY - interactionState.dragStart.y;
      
      containerRef.current.scrollLeft = interactionState.dragStart.scrollLeft - deltaX;
      containerRef.current.scrollTop = interactionState.dragStart.scrollTop - deltaY;
    });
  }, [interactionState.isDragging, interactionState.dragStart]);

  // 处理鼠标释放
  const handleMouseUp = useCallback(() => {
    setInteractionState(prev => ({
      ...prev,
      isDragging: false
    }));
    
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  }, []);

  // 处理滚轮缩放
  const handleWheel = useCallback((e: React.WheelEvent) => {
    if (!containerRef.current) return;
    
    e.preventDefault();
    
    const rect = containerRef.current.getBoundingClientRect();
    const centerX = e.clientX - rect.left;
    
    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
    const newViewport = zoomTimeline(viewport, zoomFactor, centerX, defaultTimelineConfig);
    
    onViewportChange(newViewport);
  }, [viewport, onViewportChange]);

  // 处理触摸开始
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!containerRef.current) return;
    
    if (e.touches.length === 1) {
      // 单指拖拽
      const touch = e.touches[0];
      setInteractionState(prev => ({
        ...prev,
        isDragging: true,
        dragStart: {
          x: touch.clientX,
          y: touch.clientY,
          scrollLeft: containerRef.current!.scrollLeft,
          scrollTop: containerRef.current!.scrollTop,
        }
      }));
    } else if (e.touches.length === 2) {
      // 双指缩放
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) + 
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      
      setInteractionState(prev => ({
        ...prev,
        isZooming: true,
        lastTouchDistance: distance,
        isDragging: false,
      }));
    }
  }, []);

  // 处理触摸移动
  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!containerRef.current) return;
    
    e.preventDefault();
    
    if (e.touches.length === 1 && interactionState.isDragging) {
      // 单指拖拽
      const touch = e.touches[0];
      const deltaX = touch.clientX - interactionState.dragStart.x;
      const deltaY = touch.clientY - interactionState.dragStart.y;
      
      containerRef.current.scrollLeft = interactionState.dragStart.scrollLeft - deltaX;
      containerRef.current.scrollTop = interactionState.dragStart.scrollTop - deltaY;
    } else if (e.touches.length === 2 && interactionState.isZooming) {
      // 双指缩放
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) + 
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      
      if (interactionState.lastTouchDistance > 0) {
        const zoomFactor = distance / interactionState.lastTouchDistance;
        const rect = containerRef.current.getBoundingClientRect();
        const centerX = (touch1.clientX + touch2.clientX) / 2 - rect.left;
        
        const newViewport = zoomTimeline(viewport, zoomFactor, centerX, defaultTimelineConfig);
        onViewportChange(newViewport);
      }
      
      setInteractionState(prev => ({
        ...prev,
        lastTouchDistance: distance
      }));
    }
  }, [interactionState, viewport, onViewportChange]);

  // 处理触摸结束
  const handleTouchEnd = useCallback(() => {
    setInteractionState(prev => ({
      ...prev,
      isDragging: false,
      isZooming: false,
      lastTouchDistance: 0,
    }));
  }, []);

  // 键盘导航
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!containerRef.current) return;
    
    switch (e.key) {
      case 'ArrowLeft':
        e.preventDefault();
        containerRef.current.scrollLeft -= 50;
        break;
      case 'ArrowRight':
        e.preventDefault();
        containerRef.current.scrollLeft += 50;
        break;
      case '+':
      case '=':
        e.preventDefault();
        onViewportChange({
          ...viewport,
          scale: Math.min(10, viewport.scale * 1.2)
        });
        break;
      case '-':
        e.preventDefault();
        onViewportChange({
          ...viewport,
          scale: Math.max(0.1, viewport.scale * 0.8)
        });
        break;
      case '0':
        e.preventDefault();
        onViewportChange({
          ...viewport,
          scale: 1
        });
        break;
    }
  }, [viewport, onViewportChange]);

  // 绑定全局事件
  useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => handleMouseMove(e);
    const handleGlobalMouseUp = () => handleMouseUp();
    const handleGlobalKeyDown = (e: KeyboardEvent) => handleKeyDown(e);

    if (interactionState.isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);
    }

    document.addEventListener('keydown', handleGlobalKeyDown);

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.removeEventListener('keydown', handleGlobalKeyDown);
      
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [interactionState.isDragging, handleMouseMove, handleMouseUp, handleKeyDown]);

  // 平滑滚动到指定位置
  const smoothScrollTo = useCallback((targetScrollLeft: number) => {
    if (!containerRef.current) return;
    
    const startScrollLeft = containerRef.current.scrollLeft;
    const distance = targetScrollLeft - startScrollLeft;
    const duration = 500; // 500ms
    const startTime = performance.now();
    
    const animateScroll = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // 使用 easeInOutCubic 缓动函数
      const easeProgress = progress < 0.5 
        ? 4 * progress * progress * progress 
        : 1 - Math.pow(-2 * progress + 2, 3) / 2;
      
      if (containerRef.current) {
        containerRef.current.scrollLeft = startScrollLeft + distance * easeProgress;
      }
      
      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      }
    };
    
    requestAnimationFrame(animateScroll);
  }, []);

  return {
    containerRef,
    interactionState,
    handlers: {
      onMouseDown: handleMouseDown,
      onWheel: handleWheel,
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd,
    },
    smoothScrollTo,
  };
}
