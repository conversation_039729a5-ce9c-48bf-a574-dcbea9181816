/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // 历史感配色方案
        ancient: {
          50: '#fdf8f0',
          100: '#faebd7',
          200: '#f5deb3',
          300: '#deb887',
          400: '#cd853f',
          500: '#a0522d',
          600: '#8b4513',
          700: '#654321',
          800: '#3e2723',
          900: '#2e1a0e',
        },
        imperial: {
          50: '#fff8dc',
          100: '#ffd700',
          200: '#ffb347',
          300: '#ff8c00',
          400: '#ff6347',
          500: '#dc143c',
          600: '#b22222',
          700: '#8b0000',
          800: '#660000',
          900: '#330000',
        },
        sage: {
          50: '#f0f4f0',
          100: '#d4e6d4',
          200: '#a8cca8',
          300: '#7cb37c',
          400: '#509950',
          500: '#2e7d32',
          600: '#1b5e20',
          700: '#0d4f0d',
          800: '#0a3f0a',
          900: '#062f06',
        }
      },
      fontFamily: {
        'chinese': ['Noto Serif SC', 'serif'],
        'title': ['Ma Shan Zheng', 'cursive'],
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'float': 'float 3s ease-in-out infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        glow: {
          '0%': { boxShadow: '0 0 5px rgba(255, 215, 0, 0.5)' },
          '100%': { boxShadow: '0 0 20px rgba(255, 215, 0, 0.8)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
      },
    },
  },
  plugins: [],
}
