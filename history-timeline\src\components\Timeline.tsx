import React, { useCallback } from 'react';
import { motion } from 'framer-motion';
import { HistoryPeriod, TimelineViewport } from '../types/history';
import { useTimelineInteraction } from '../hooks/useTimelineInteraction';
import { Tooltip } from './Tooltip';
import {
  defaultTimelineConfig,
  yearToPixel,
  getTimelineWidth,
  calculatePeriodPosition,
  generateTimeMarks,
  formatYear
} from '../utils/timelineUtils';

interface TimelineProps {
  periods: HistoryPeriod[];
  viewport: TimelineViewport;
  onPeriodClick: (period: HistoryPeriod) => void;
  onViewportChange: (viewport: TimelineViewport) => void;
  title: string;
  className?: string;
}

export const Timeline: React.FC<TimelineProps> = ({
  periods,
  viewport,
  onPeriodClick,
  onViewportChange,
  title,
  className = ''
}) => {
  const timelineWidth = getTimelineWidth(defaultTimelineConfig, viewport);

  // 使用增强的交互钩子
  const { containerRef, interactionState, handlers, smoothScrollTo } = useTimelineInteraction({
    viewport,
    onViewportChange
  });

  // 处理时期点击，添加平滑滚动
  const handlePeriodClickWithScroll = useCallback((period: HistoryPeriod) => {
    onPeriodClick(period);

    // 计算时期在时间轴上的位置，滚动到中心
    const position = calculatePeriodPosition(period, defaultTimelineConfig, viewport);
    const containerWidth = containerRef.current?.clientWidth || 0;
    const targetScrollLeft = position.left - containerWidth / 2 + position.width / 2;

    smoothScrollTo(Math.max(0, targetScrollLeft));
  }, [onPeriodClick, viewport, smoothScrollTo]);

  // 生成时间刻度
  const timeMarks = generateTimeMarks(
    viewport.startYear,
    viewport.endYear,
    defaultTimelineConfig
  );

  return (
    <div className={`timeline-wrapper ${className}`}>
      {/* 时间轴标题 */}
      <div className="timeline-header mb-4">
        <h2 className="text-xl font-bold text-ancient-800 font-chinese">{title}</h2>
      </div>

      {/* 时间轴容器 */}
      <div
        ref={containerRef}
        className={`timeline-container relative overflow-x-auto overflow-y-hidden bg-gradient-to-r from-ancient-100 to-ancient-200 rounded-lg shadow-lg transition-all duration-200 ${
          interactionState.isDragging ? 'cursor-grabbing' : 'cursor-grab'
        }`}
        style={{ height: `${defaultTimelineConfig.height + 60}px` }}
        {...handlers}
      >
        {/* 时间轴背景 */}
        <div 
          className="timeline-background relative"
          style={{ width: `${timelineWidth}px`, height: '100%' }}
        >
          {/* 时间刻度 */}
          <div className="absolute top-0 left-0 w-full h-full">
            {timeMarks.map((mark) => {
              const position = yearToPixel(mark.year, defaultTimelineConfig, viewport);
              return (
                <div
                  key={mark.year}
                  className="absolute top-0 flex flex-col items-center"
                  style={{ left: `${position}px` }}
                >
                  <div 
                    className={`w-px bg-ancient-600 ${
                      mark.type === 'major' ? 'h-8' : 'h-4'
                    }`}
                  />
                  <span 
                    className={`text-xs text-ancient-700 mt-1 whitespace-nowrap ${
                      mark.type === 'major' ? 'font-semibold' : 'font-normal'
                    }`}
                  >
                    {mark.type === 'major' ? mark.label : ''}
                  </span>
                </div>
              );
            })}
          </div>

          {/* 历史时期条 */}
          <div className="absolute top-12 left-0 w-full" style={{ height: '80px' }}>
            {periods.map((period) => {
              const position = calculatePeriodPosition(period, defaultTimelineConfig, viewport);
              
              return (
                <Tooltip key={period.id} period={period}>
                  <motion.div
                    className="absolute top-2 rounded-lg shadow-md cursor-pointer border-2 border-ancient-600 hover:border-imperial-400 transition-all duration-300"
                    style={{
                      left: `${position.left}px`,
                      width: `${position.width}px`,
                      height: '60px',
                      backgroundColor: period.color,
                    }}
                    onClick={() => handlePeriodClickWithScroll(period)}
                    whileHover={{
                      scale: 1.05,
                      boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                      zIndex: 10
                    }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {/* 时期名称 */}
                    <div className="p-2 h-full flex flex-col justify-center">
                      <div className="text-white font-semibold text-sm font-chinese text-center leading-tight">
                        {period.name}
                      </div>
                      <div className="text-white text-xs text-center opacity-90">
                        {formatYear(period.startYear)} - {formatYear(period.endYear)}
                      </div>
                    </div>
                  </motion.div>
                </Tooltip>
              );
            })}
          </div>
        </div>
      </div>

      {/* 缩放控制 */}
      <div className="timeline-controls mt-4 flex justify-center space-x-4">
        <button
          className="px-4 py-2 bg-ancient-600 text-white rounded-lg hover:bg-ancient-700 transition-colors"
          onClick={() => onViewportChange({ ...viewport, scale: viewport.scale * 0.8 })}
        >
          缩小
        </button>
        <span className="px-4 py-2 bg-ancient-100 text-ancient-800 rounded-lg">
          缩放: {Math.round(viewport.scale * 100)}%
        </span>
        <button
          className="px-4 py-2 bg-ancient-600 text-white rounded-lg hover:bg-ancient-700 transition-colors"
          onClick={() => onViewportChange({ ...viewport, scale: viewport.scale * 1.25 })}
        >
          放大
        </button>
      </div>
    </div>
  );
};
