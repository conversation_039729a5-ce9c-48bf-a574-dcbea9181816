import React, { useState, useEffect } from 'react';

function App() {
  const [isLoading, setIsLoading] = useState(true);

  // 简化的加载过程
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #fef7ed 0%, #fed7aa 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '64px',
            height: '64px',
            border: '4px solid #fb923c',
            borderTop: '4px solid #ea580c',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#9a3412', marginBottom: '8px' }}>
            历史长河正在展开...
          </h2>
          <p style={{ color: '#c2410c' }}>正在加载历史数据</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <style>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #fef7ed 0%, #fed7aa 100%)'
      }}>
        {/* 标题区域 */}
        <header style={{ textAlign: 'center', padding: '48px 0' }}>
          <h1 style={{
            fontSize: '36px',
            fontWeight: 'bold',
            color: '#7c2d12',
            marginBottom: '16px',
            fontFamily: '"Noto Serif SC", serif'
          }}>
            中华文明与世界文明对比时间轴
          </h1>
          <p style={{
            fontSize: '18px',
            color: '#c2410c',
            fontFamily: '"Noto Serif SC", serif'
          }}>
            探索中国历史与同时期世界文明的发展脉络
          </p>
        </header>

        {/* 主要内容区域 */}
        <main style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '0 16px 32px'
        }}>
          <div style={{
            background: 'white',
            borderRadius: '12px',
            boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
            padding: '32px'
          }}>
            <h2 style={{
              fontSize: '24px',
              fontWeight: 'bold',
              color: '#374151',
              marginBottom: '24px',
              fontFamily: '"Noto Serif SC", serif'
            }}>
              时间轴功能演示
            </h2>

            {/* 中国历史时间轴 */}
            <div style={{ marginBottom: '32px' }}>
              <h3 style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#4b5563',
                marginBottom: '16px',
                fontFamily: '"Noto Serif SC", serif'
              }}>
                中国历史
              </h3>
              <div style={{
                background: 'linear-gradient(to right, #fecaca, #fca5a5)',
                borderRadius: '8px',
                padding: '24px'
              }}>
                <div style={{
                  display: 'flex',
                  gap: '16px',
                  overflowX: 'auto',
                  paddingBottom: '8px'
                }}>
                  <div style={{
                    flexShrink: 0,
                    background: '#dc2626',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    fontFamily: '"Noto Serif SC", serif',
                    fontSize: '14px'
                  }}>
                    夏朝 (约前2070-前1600年)
                  </div>
                  <div style={{
                    flexShrink: 0,
                    background: '#dc2626',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    fontFamily: '"Noto Serif SC", serif',
                    fontSize: '14px'
                  }}>
                    商朝 (约前1600-前1046年)
                  </div>
                  <div style={{
                    flexShrink: 0,
                    background: '#dc2626',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    fontFamily: '"Noto Serif SC", serif',
                    fontSize: '14px'
                  }}>
                    西周 (约前1046-前771年)
                  </div>
                  <div style={{
                    flexShrink: 0,
                    background: '#dc2626',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    fontFamily: '"Noto Serif SC", serif',
                    fontSize: '14px'
                  }}>
                    东周 (前770-前256年)
                  </div>
                  <div style={{
                    flexShrink: 0,
                    background: '#dc2626',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    fontFamily: '"Noto Serif SC", serif',
                    fontSize: '14px'
                  }}>
                    秦朝 (前221-前206年)
                  </div>
                </div>
              </div>
            </div>

            {/* 世界历史时间轴 */}
            <div style={{ marginBottom: '32px' }}>
              <h3 style={{
                fontSize: '20px',
                fontWeight: '600',
                color: '#4b5563',
                marginBottom: '16px',
                fontFamily: '"Noto Serif SC", serif'
              }}>
                世界历史
              </h3>
              <div style={{
                background: 'linear-gradient(to right, #bfdbfe, #93c5fd)',
                borderRadius: '8px',
                padding: '24px'
              }}>
                <div style={{
                  display: 'flex',
                  gap: '16px',
                  overflowX: 'auto',
                  paddingBottom: '8px'
                }}>
                  <div style={{
                    flexShrink: 0,
                    background: '#2563eb',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    fontFamily: '"Noto Serif SC", serif',
                    fontSize: '14px'
                  }}>
                    美索不达米亚 (前3500-前2000年)
                  </div>
                  <div style={{
                    flexShrink: 0,
                    background: '#2563eb',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    fontFamily: '"Noto Serif SC", serif',
                    fontSize: '14px'
                  }}>
                    古埃及 (前2686-前2181年)
                  </div>
                  <div style={{
                    flexShrink: 0,
                    background: '#2563eb',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    fontFamily: '"Noto Serif SC", serif',
                    fontSize: '14px'
                  }}>
                    印度河文明 (前2600-前1900年)
                  </div>
                  <div style={{
                    flexShrink: 0,
                    background: '#2563eb',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    fontFamily: '"Noto Serif SC", serif',
                    fontSize: '14px'
                  }}>
                    古希腊 (前800-前146年)
                  </div>
                  <div style={{
                    flexShrink: 0,
                    background: '#2563eb',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    fontFamily: '"Noto Serif SC", serif',
                    fontSize: '14px'
                  }}>
                    罗马共和国 (前509-前27年)
                  </div>
                </div>
              </div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <p style={{
                color: '#6b7280',
                fontFamily: '"Noto Serif SC", serif',
                lineHeight: '1.6'
              }}>
                这是一个简化版本，展示了基本的时间轴概念。
                <br />
                完整版本将包含交互式拖拽、缩放和详细信息展示功能。
              </p>
            </div>
          </div>
        </main>

        {/* 页脚 */}
        <footer style={{
          background: '#1f2937',
          color: '#f3f4f6',
          padding: '32px 0',
          marginTop: '64px'
        }}>
          <div style={{
            maxWidth: '1200px',
            margin: '0 auto',
            padding: '0 16px',
            textAlign: 'center'
          }}>
            <p style={{ fontFamily: '"Noto Serif SC", serif' }}>
              中华文明与世界文明对比时间轴 - 探索历史，启迪未来
            </p>
            <p style={{
              fontSize: '14px',
              color: '#d1d5db',
              marginTop: '8px',
              fontFamily: '"Noto Serif SC", serif'
            }}>
              专为中小学生历史教育设计
            </p>
          </div>
        </footer>
      </div>
    </>
  );
}

export default App;
