import React, { useState, useEffect } from 'react';
import { DualTimeline } from './components/DualTimeline';
import { HistoryDetailPanel } from './components/HistoryDetailPanel';
import { BackgroundDecorations } from './components/BackgroundDecorations';
import { LoadingAnimation } from './components/LoadingAnimation';
import { KeyboardShortcuts } from './components/KeyboardShortcuts';
import { SelectedPeriod } from './types/history';
import { getHistoryComparison } from './data';

function App() {
  const [selectedPeriod, setSelectedPeriod] = useState<SelectedPeriod | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 模拟加载过程
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const handlePeriodSelect = (selection: SelectedPeriod) => {
    setSelectedPeriod(selection);
  };

  const historyComparison = selectedPeriod ? getHistoryComparison(selectedPeriod.year) : null;

  if (isLoading) {
    return <LoadingAnimation />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-ancient-50 via-ancient-100 to-sage-50 relative">
      {/* 背景装饰 */}
      <BackgroundDecorations />

      {/* 主要内容区域 */}
      <main className="relative z-10 container mx-auto px-4 py-8">
        {/* 双时间轴组件 */}
        <DualTimeline onPeriodSelect={handlePeriodSelect} />

        {/* 历史详情面板 */}
        {selectedPeriod && historyComparison && (
          <div className="mt-12">
            <HistoryDetailPanel
              selectedPeriod={selectedPeriod}
              comparison={historyComparison}
            />
          </div>
        )}
      </main>

      {/* 页脚 */}
      <footer className="relative z-10 bg-ancient-800 text-ancient-100 py-8 mt-16">
        <div className="container mx-auto px-4 text-center">
          <p className="font-chinese">
            中华文明与世界文明对比时间轴 - 探索历史，启迪未来
          </p>
          <p className="text-sm text-ancient-300 mt-2">
            专为中小学生历史教育设计
          </p>
        </div>
      </footer>

      {/* 键盘快捷键帮助 */}
      <KeyboardShortcuts />
    </div>
  );
}

export default App;
