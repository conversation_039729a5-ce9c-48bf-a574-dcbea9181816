// 性能监控工具

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage?: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    loadTime: 0,
    renderTime: 0,
    interactionTime: 0
  };

  private startTimes: Map<string, number> = new Map();

  // 开始计时
  startTiming(label: string): void {
    this.startTimes.set(label, performance.now());
  }

  // 结束计时并返回耗时
  endTiming(label: string): number {
    const startTime = this.startTimes.get(label);
    if (!startTime) {
      console.warn(`No start time found for label: ${label}`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.startTimes.delete(label);
    return duration;
  }

  // 记录页面加载时间
  recordLoadTime(): void {
    if (typeof window !== 'undefined' && window.performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        this.metrics.loadTime = navigation.loadEventEnd - navigation.navigationStart;
      }
    }
  }

  // 记录渲染时间
  recordRenderTime(duration: number): void {
    this.metrics.renderTime = duration;
  }

  // 记录交互时间
  recordInteractionTime(duration: number): void {
    this.metrics.interactionTime = Math.max(this.metrics.interactionTime, duration);
  }

  // 获取内存使用情况
  getMemoryUsage(): number | undefined {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize / 1024 / 1024; // 转换为MB
    }
    return undefined;
  }

  // 获取所有指标
  getMetrics(): PerformanceMetrics {
    return {
      ...this.metrics,
      memoryUsage: this.getMemoryUsage()
    };
  }

  // 检查性能是否良好
  isPerformanceGood(): boolean {
    const metrics = this.getMetrics();
    return (
      metrics.loadTime < 3000 && // 加载时间小于3秒
      metrics.renderTime < 100 && // 渲染时间小于100ms
      metrics.interactionTime < 50 && // 交互时间小于50ms
      (!metrics.memoryUsage || metrics.memoryUsage < 100) // 内存使用小于100MB
    );
  }

  // 生成性能报告
  generateReport(): string {
    const metrics = this.getMetrics();
    const isGood = this.isPerformanceGood();

    return `
性能报告 (${new Date().toLocaleString()}):
- 页面加载时间: ${metrics.loadTime.toFixed(2)}ms ${metrics.loadTime < 3000 ? '✅' : '❌'}
- 渲染时间: ${metrics.renderTime.toFixed(2)}ms ${metrics.renderTime < 100 ? '✅' : '❌'}
- 交互响应时间: ${metrics.interactionTime.toFixed(2)}ms ${metrics.interactionTime < 50 ? '✅' : '❌'}
${metrics.memoryUsage ? `- 内存使用: ${metrics.memoryUsage.toFixed(2)}MB ${metrics.memoryUsage < 100 ? '✅' : '❌'}` : ''}
- 整体评价: ${isGood ? '良好 ✅' : '需要优化 ❌'}
    `.trim();
  }

  // 监控Core Web Vitals
  observeWebVitals(): void {
    if (typeof window === 'undefined') return;

    // 监控LCP (Largest Contentful Paint)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          console.log('LCP:', lastEntry.startTime);
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

        // 监控FID (First Input Delay)
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            console.log('FID:', entry.processingStart - entry.startTime);
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });

        // 监控CLS (Cumulative Layout Shift)
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0;
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          console.log('CLS:', clsValue);
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      } catch (error) {
        console.warn('Performance monitoring not supported:', error);
      }
    }
  }

  // 监控长任务
  observeLongTasks(): void {
    if (typeof window === 'undefined') return;

    if ('PerformanceObserver' in window) {
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            console.warn('Long task detected:', entry.duration, 'ms');
          });
        });
        longTaskObserver.observe({ entryTypes: ['longtask'] });
      } catch (error) {
        console.warn('Long task monitoring not supported:', error);
      }
    }
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// 自动开始监控
if (typeof window !== 'undefined') {
  // 页面加载完成后记录加载时间
  window.addEventListener('load', () => {
    setTimeout(() => {
      performanceMonitor.recordLoadTime();
    }, 0);
  });

  // 开始监控Web Vitals和长任务
  performanceMonitor.observeWebVitals();
  performanceMonitor.observeLongTasks();
}

// 导出性能监控钩子
export function usePerformanceMonitoring() {
  const startTiming = (label: string) => performanceMonitor.startTiming(label);
  const endTiming = (label: string) => performanceMonitor.endTiming(label);
  const getMetrics = () => performanceMonitor.getMetrics();
  const generateReport = () => performanceMonitor.generateReport();

  return {
    startTiming,
    endTiming,
    getMetrics,
    generateReport
  };
}