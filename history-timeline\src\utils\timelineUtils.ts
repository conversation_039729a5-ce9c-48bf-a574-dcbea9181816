import { TimelineConfig, TimelineViewport, HistoryPeriod } from '../types/history';

// 默认时间轴配置
export const defaultTimelineConfig: TimelineConfig = {
  height: 120,
  minYear: -3500,
  maxYear: 2024,
  pixelsPerYear: 0.5, // 每年0.5像素，可以通过缩放调整
  majorTickInterval: 500, // 主要刻度每500年
  minorTickInterval: 100, // 次要刻度每100年
};

// 年份转换为像素位置
export function yearToPixel(year: number, config: TimelineConfig, viewport: TimelineViewport): number {
  const totalYears = config.maxYear - config.minYear;
  const yearRatio = (year - config.minYear) / totalYears;
  return yearRatio * (totalYears * config.pixelsPerYear * viewport.scale);
}

// 像素位置转换为年份
export function pixelToYear(pixel: number, config: TimelineConfig, viewport: TimelineViewport): number {
  const totalYears = config.maxYear - config.minYear;
  const totalWidth = totalYears * config.pixelsPerYear * viewport.scale;
  const yearRatio = pixel / totalWidth;
  return config.minYear + (yearRatio * totalYears);
}

// 计算时间轴的总宽度
export function getTimelineWidth(config: TimelineConfig, viewport: TimelineViewport): number {
  const totalYears = config.maxYear - config.minYear;
  return totalYears * config.pixelsPerYear * viewport.scale;
}

// 获取可见范围内的年份
export function getVisibleYearRange(
  scrollLeft: number, 
  containerWidth: number, 
  config: TimelineConfig, 
  viewport: TimelineViewport
): { startYear: number; endYear: number } {
  const startYear = pixelToYear(scrollLeft, config, viewport);
  const endYear = pixelToYear(scrollLeft + containerWidth, config, viewport);
  return { startYear, endYear };
}

// 生成时间刻度
export function generateTimeMarks(
  startYear: number, 
  endYear: number, 
  config: TimelineConfig
): Array<{ year: number; type: 'major' | 'minor'; label: string }> {
  const marks: Array<{ year: number; type: 'major' | 'minor'; label: string }> = [];
  
  // 计算起始年份（对齐到刻度间隔）
  const majorStart = Math.floor(startYear / config.majorTickInterval) * config.majorTickInterval;
  const minorStart = Math.floor(startYear / config.minorTickInterval) * config.minorTickInterval;
  
  // 生成主要刻度
  for (let year = majorStart; year <= endYear; year += config.majorTickInterval) {
    if (year >= startYear) {
      marks.push({
        year,
        type: 'major',
        label: formatYear(year)
      });
    }
  }
  
  // 生成次要刻度
  for (let year = minorStart; year <= endYear; year += config.minorTickInterval) {
    if (year >= startYear && year % config.majorTickInterval !== 0) {
      marks.push({
        year,
        type: 'minor',
        label: formatYear(year)
      });
    }
  }
  
  return marks.sort((a, b) => a.year - b.year);
}

// 格式化年份显示
export function formatYear(year: number): string {
  if (year < 0) {
    return `公元前${Math.abs(year)}年`;
  } else if (year === 0) {
    return '公元元年';
  } else {
    return `公元${year}年`;
  }
}

// 计算历史时期在时间轴上的位置和宽度
export function calculatePeriodPosition(
  period: HistoryPeriod,
  config: TimelineConfig,
  viewport: TimelineViewport
): { left: number; width: number } {
  const startPixel = yearToPixel(period.startYear, config, viewport);
  const endPixel = yearToPixel(period.endYear, config, viewport);
  
  return {
    left: startPixel,
    width: Math.max(endPixel - startPixel, 20) // 最小宽度20像素
  };
}

// 检查两个时期是否重叠
export function periodsOverlap(period1: HistoryPeriod, period2: HistoryPeriod): boolean {
  return !(period1.endYear < period2.startYear || period2.endYear < period1.startYear);
}

// 计算时期的垂直布局（避免重叠）
export function calculatePeriodLayers(periods: HistoryPeriod[]): Map<string, number> {
  const layers = new Map<string, number>();
  const sortedPeriods = [...periods].sort((a, b) => a.startYear - b.startYear);
  
  for (const period of sortedPeriods) {
    let layer = 0;
    let hasOverlap = true;
    
    while (hasOverlap) {
      hasOverlap = false;
      for (const [otherId, otherLayer] of layers.entries()) {
        if (otherLayer === layer) {
          const otherPeriod = periods.find(p => p.id === otherId);
          if (otherPeriod && periodsOverlap(period, otherPeriod)) {
            hasOverlap = true;
            break;
          }
        }
      }
      if (hasOverlap) {
        layer++;
      }
    }
    
    layers.set(period.id, layer);
  }
  
  return layers;
}

// 缩放时间轴
export function zoomTimeline(
  viewport: TimelineViewport,
  zoomFactor: number,
  centerX: number,
  config: TimelineConfig
): TimelineViewport {
  const newScale = Math.max(0.1, Math.min(10, viewport.scale * zoomFactor));
  
  // 计算缩放中心对应的年份
  const centerYear = pixelToYear(centerX, config, viewport);
  
  return {
    ...viewport,
    scale: newScale,
    centerYear
  };
}

// 平移时间轴
export function panTimeline(
  viewport: TimelineViewport,
  deltaX: number,
  config: TimelineConfig
): TimelineViewport {
  const deltaYears = deltaX / (config.pixelsPerYear * viewport.scale);
  const newCenterYear = Math.max(
    config.minYear,
    Math.min(config.maxYear, viewport.centerYear - deltaYears)
  );
  
  return {
    ...viewport,
    centerYear: newCenterYear
  };
}
