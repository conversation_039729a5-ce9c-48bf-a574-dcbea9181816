import { HistoryPeriod } from '../types/history';

export const worldHistoryData: HistoryPeriod[] = [
  {
    id: 'mesopotamia-early',
    name: '早期美索不达米亚文明',
    startYear: -3500,
    endYear: -2000,
    region: 'world',
    color: '#8B0000',
    description: '人类最早的文明之一，发源于两河流域，创造了楔形文字和城邦制度。',
    capital: '乌尔、乌鲁克、巴比伦',
    territory: '美索不达米亚平原（今伊拉克）',
    politicalSystem: '城邦制',
    keyEvents: [
      {
        id: 'cuneiform-writing',
        year: -3200,
        title: '楔形文字出现',
        description: '苏美尔人创造了世界上最早的文字系统',
        significance: '人类文明史上的重要里程碑',
        type: 'cultural',
        impact: 'global'
      },
      {
        id: 'sargon-empire',
        year: -2334,
        title: '萨尔贡建立阿卡德帝国',
        description: '萨尔贡统一美索不达米亚，建立第一个多民族帝国',
        significance: '世界上第一个真正的帝国',
        type: 'political',
        impact: 'regional'
      }
    ],
    culturalFeatures: ['楔形文字', '城邦文化', '宗教神话', '法律制度'],
    economicFeatures: ['农业灌溉', '手工业', '商业贸易', '金属冶炼'],
    notablePersons: [
      {
        id: 'sargon',
        name: '萨尔贡大帝',
        birthYear: -2370,
        deathYear: -2315,
        role: '阿卡德帝国建立者',
        achievements: ['统一美索不达米亚', '建立第一个帝国'],
        significance: '古代世界第一位皇帝'
      }
    ],
    achievements: ['楔形文字', '城市规划', '法律条文', '天文历法']
  },
  {
    id: 'egypt-old-kingdom',
    name: '古埃及古王国时期',
    startYear: -2686,
    endYear: -2181,
    region: 'world',
    color: '#FFD700',
    description: '古埃及的黄金时代，金字塔建造的鼎盛期，法老权力达到顶峰。',
    capital: '孟菲斯',
    territory: '尼罗河流域',
    politicalSystem: '神权专制',
    keyEvents: [
      {
        id: 'great-pyramid',
        year: -2580,
        title: '胡夫金字塔建成',
        description: '古代世界七大奇迹之一的胡夫金字塔建成',
        significance: '古代建筑技术的巅峰之作',
        type: 'cultural',
        impact: 'global'
      },
      {
        id: 'sphinx-built',
        year: -2558,
        title: '狮身人面像建造',
        description: '哈夫拉法老下令建造狮身人面像',
        significance: '古埃及艺术的杰作',
        type: 'cultural',
        impact: 'regional'
      }
    ],
    culturalFeatures: ['象形文字', '金字塔建筑', '木乃伊制作', '太阳神崇拜'],
    economicFeatures: ['尼罗河农业', '手工业发达', '对外贸易', '建筑工程'],
    notablePersons: [
      {
        id: 'khufu',
        name: '胡夫法老',
        birthYear: -2589,
        deathYear: -2566,
        role: '第四王朝法老',
        achievements: ['建造胡夫金字塔', '统治古王国鼎盛期'],
        significance: '古埃及最著名的法老之一'
      }
    ],
    achievements: ['金字塔建筑', '象形文字', '医学知识', '天文观测']
  },
  {
    id: 'indus-valley',
    name: '印度河流域文明',
    startYear: -2600,
    endYear: -1900,
    region: 'world',
    color: '#4169E1',
    description: '古代世界四大文明之一，以其先进的城市规划和排水系统而闻名。',
    capital: '哈拉帕、摩亨佐达罗',
    territory: '印度河流域（今巴基斯坦、印度西北部）',
    politicalSystem: '城邦联盟',
    keyEvents: [
      {
        id: 'harappa-peak',
        year: -2300,
        title: '哈拉帕文明鼎盛期',
        description: '印度河流域文明达到鼎盛，城市规划达到很高水平',
        significance: '古代城市规划的典范',
        type: 'cultural',
        impact: 'regional'
      },
      {
        id: 'indus-decline',
        year: -1900,
        title: '印度河文明衰落',
        description: '由于气候变化和其他因素，印度河文明开始衰落',
        significance: '古代文明的消失之谜',
        type: 'social',
        impact: 'regional'
      }
    ],
    culturalFeatures: ['印章文字', '城市规划', '排水系统', '标准化度量'],
    economicFeatures: ['农业发达', '手工业精湛', '对外贸易', '标准化生产'],
    notablePersons: [],
    achievements: ['城市规划', '排水系统', '度量标准', '贸易网络']
  },
  {
    id: 'minoan-civilization',
    name: '米诺斯文明',
    startYear: -2700,
    endYear: -1100,
    region: 'world',
    color: '#20B2AA',
    description: '欧洲最早的文明，以其海上贸易和精美的艺术而著称。',
    capital: '克诺索斯',
    territory: '克里特岛及爱琴海诸岛',
    politicalSystem: '宫殿制度',
    keyEvents: [
      {
        id: 'knossos-palace',
        year: -2000,
        title: '克诺索斯宫殿建成',
        description: '米诺斯文明的政治和宗教中心建成',
        significance: '欧洲最早的宫殿建筑',
        type: 'cultural',
        impact: 'regional'
      },
      {
        id: 'linear-a',
        year: -1800,
        title: '线形文字A出现',
        description: '米诺斯人创造了自己的文字系统',
        significance: '欧洲早期文字发展',
        type: 'cultural',
        impact: 'regional'
      }
    ],
    culturalFeatures: ['线形文字A', '宫殿艺术', '海洋文化', '女神崇拜'],
    economicFeatures: ['海上贸易', '农业生产', '手工业', '渔业'],
    notablePersons: [],
    achievements: ['海上贸易', '宫殿建筑', '艺术创作', '文字系统']
  },
  {
    id: 'ancient-greece',
    name: '古希腊文明',
    startYear: -800,
    endYear: -146,
    region: 'world',
    color: '#4682B4',
    description: '古代欧洲最重要的文明之一，创造了民主制度、哲学思辨和科学精神。',
    capital: '雅典、斯巴达等城邦',
    territory: '希腊半岛、爱琴海诸岛、小亚细亚沿岸',
    politicalSystem: '城邦制度',
    keyEvents: [
      {
        id: 'olympics',
        year: -776,
        title: '第一届奥林匹克运动会',
        description: '在奥林匹亚举行第一届奥运会',
        significance: '体育竞技传统的开始',
        type: 'cultural',
        impact: 'global'
      },
      {
        id: 'democracy',
        year: -508,
        title: '雅典民主制建立',
        description: '克里斯提尼改革，建立民主制度',
        significance: '人类历史上第一个民主制度',
        type: 'political',
        impact: 'global'
      }
    ],
    culturalFeatures: ['民主制度', '哲学思辨', '戏剧艺术', '奥林匹克'],
    economicFeatures: ['海上贸易', '殖民扩张', '手工业发达', '货币经济'],
    notablePersons: [
      {
        id: 'socrates',
        name: '苏格拉底',
        birthYear: -470,
        deathYear: -399,
        role: '哲学家',
        achievements: ['苏格拉底方法', '道德哲学', '理性思辨'],
        significance: '西方哲学的奠基人之一'
      }
    ],
    achievements: ['民主制度', '哲学思想', '科学精神', '艺术成就']
  },
  {
    id: 'roman-republic',
    name: '罗马共和国',
    startYear: -509,
    endYear: -27,
    region: 'world',
    color: '#8B0000',
    description: '地中海世界的霸主，建立了完善的法律体系和军事制度。',
    capital: '罗马',
    territory: '地中海沿岸大部分地区',
    politicalSystem: '共和制',
    keyEvents: [
      {
        id: 'republic-founded',
        year: -509,
        title: '罗马共和国建立',
        description: '推翻王制，建立共和政体',
        significance: '共和制度的典型代表',
        type: 'political',
        impact: 'regional'
      },
      {
        id: 'punic-wars',
        year: -264,
        title: '布匿战争开始',
        description: '与迦太基争夺地中海霸权',
        significance: '确立罗马地中海霸权',
        type: 'military',
        impact: 'regional'
      }
    ],
    culturalFeatures: ['罗马法', '拉丁文学', '建筑工程', '军事文化'],
    economicFeatures: ['奴隶制经济', '商业贸易', '农业生产', '手工业'],
    notablePersons: [
      {
        id: 'caesar',
        name: '凯撒',
        birthYear: -100,
        deathYear: -44,
        role: '政治家、军事家',
        achievements: ['征服高卢', '改革历法', '扩张版图'],
        significance: '罗马历史上最重要的人物之一'
      }
    ],
    achievements: ['罗马法', '共和制度', '军事征服', '工程技术']
  },
  {
    id: 'persian-empire',
    name: '波斯帝国',
    startYear: -550,
    endYear: -331,
    region: 'world',
    color: '#DAA520',
    description: '古代世界最大的帝国之一，连接东西方文明的桥梁。',
    capital: '波斯波利斯、苏萨',
    territory: '从印度河到地中海的广大地区',
    politicalSystem: '专制君主制',
    keyEvents: [
      {
        id: 'cyrus-conquest',
        year: -539,
        title: '居鲁士征服巴比伦',
        description: '波斯帝国征服新巴比伦王国',
        significance: '建立横跨三大洲的大帝国',
        type: 'military',
        impact: 'regional'
      },
      {
        id: 'persian-wars',
        year: -499,
        title: '希波战争',
        description: '波斯与希腊城邦的战争',
        significance: '东西方文明的首次大碰撞',
        type: 'military',
        impact: 'regional'
      }
    ],
    culturalFeatures: ['祆教', '多元文化', '宽容政策', '皇家大道'],
    economicFeatures: ['贸易网络', '农业税收', '手工业发达', '货币制度'],
    notablePersons: [
      {
        id: 'cyrus',
        name: '居鲁士大帝',
        birthYear: -600,
        deathYear: -530,
        role: '波斯帝国创建者',
        achievements: ['建立波斯帝国', '宽容政策', '解放巴比伦之囚'],
        significance: '古代世界最伟大的征服者之一'
      }
    ],
    achievements: ['帝国制度', '宽容政策', '交通网络', '文化融合']
  }
];

// 导出函数获取指定年份的世界历史时期
export function getWorldPeriodsByYear(year: number): HistoryPeriod[] {
  return worldHistoryData.filter(period => 
    year >= period.startYear && year <= period.endYear
  );
}

// 导出函数获取指定时间范围的世界历史时期
export function getWorldPeriodsInRange(startYear: number, endYear: number): HistoryPeriod[] {
  return worldHistoryData.filter(period => 
    period.endYear >= startYear && period.startYear <= endYear
  );
}
