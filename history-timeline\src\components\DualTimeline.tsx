import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Timeline } from './Timeline';
import { HistoryPeriod, TimelineViewport, SelectedPeriod } from '../types/history';
import { timelineData } from '../data';

interface DualTimelineProps {
  onPeriodSelect: (selection: SelectedPeriod) => void;
}

export const DualTimeline: React.FC<DualTimelineProps> = ({ onPeriodSelect }) => {
  // 时间轴视图状态
  const [viewport, setViewport] = useState<TimelineViewport>({
    startYear: -3000,
    endYear: 0,
    scale: 1,
    centerYear: -1500
  });

  // 选中的历史时期
  const [selectedPeriods, setSelectedPeriods] = useState<SelectedPeriod>({
    year: -2000
  });

  // 处理中国历史时期点击
  const handleChinaPeriodClick = useCallback((period: HistoryPeriod) => {
    const newSelection = {
      ...selectedPeriods,
      china: period,
      year: Math.floor((period.startYear + period.endYear) / 2)
    };
    setSelectedPeriods(newSelection);
    onPeriodSelect(newSelection);
  }, [selectedPeriods, onPeriodSelect]);

  // 处理世界历史时期点击
  const handleWorldPeriodClick = useCallback((period: HistoryPeriod) => {
    const newSelection = {
      ...selectedPeriods,
      world: period,
      year: Math.floor((period.startYear + period.endYear) / 2)
    };
    setSelectedPeriods(newSelection);
    onPeriodSelect(newSelection);
  }, [selectedPeriods, onPeriodSelect]);

  // 处理视图变化（同步两个时间轴）
  const handleViewportChange = useCallback((newViewport: TimelineViewport) => {
    setViewport(newViewport);
  }, []);

  // 快速导航到特定时期
  const navigateToYear = useCallback((year: number) => {
    setViewport(prev => ({
      ...prev,
      centerYear: year,
      startYear: year - 500,
      endYear: year + 500
    }));
  }, []);

  return (
    <div className="dual-timeline-container w-full max-w-7xl mx-auto p-6">
      {/* 标题区域 */}
      <motion.div 
        className="text-center mb-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h1 className="text-4xl font-bold text-ancient-900 font-title mb-4">
          中华文明与世界文明对比时间轴
        </h1>
        <p className="text-lg text-ancient-700 font-chinese">
          探索中国历史与同时期世界文明的发展脉络
        </p>
      </motion.div>

      {/* 快速导航 */}
      <motion.div 
        className="navigation-bar mb-6 flex justify-center space-x-4 flex-wrap"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <button
          onClick={() => navigateToYear(-2000)}
          className="px-4 py-2 bg-imperial-500 text-white rounded-lg hover:bg-imperial-600 transition-colors font-chinese"
        >
          夏商周时期
        </button>
        <button
          onClick={() => navigateToYear(-500)}
          className="px-4 py-2 bg-imperial-500 text-white rounded-lg hover:bg-imperial-600 transition-colors font-chinese"
        >
          春秋战国
        </button>
        <button
          onClick={() => navigateToYear(0)}
          className="px-4 py-2 bg-imperial-500 text-white rounded-lg hover:bg-imperial-600 transition-colors font-chinese"
        >
          秦汉时期
        </button>
        <button
          onClick={() => navigateToYear(500)}
          className="px-4 py-2 bg-imperial-500 text-white rounded-lg hover:bg-imperial-600 transition-colors font-chinese"
        >
          魏晋南北朝
        </button>
        <button
          onClick={() => navigateToYear(1000)}
          className="px-4 py-2 bg-imperial-500 text-white rounded-lg hover:bg-imperial-600 transition-colors font-chinese"
        >
          唐宋时期
        </button>
      </motion.div>

      {/* 中国历史时间轴 */}
      <motion.div
        initial={{ opacity: 0, x: -50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="mb-8"
      >
        <Timeline
          periods={timelineData.chinaTimeline}
          viewport={viewport}
          onPeriodClick={handleChinaPeriodClick}
          onViewportChange={handleViewportChange}
          title="中国历史"
          className="china-timeline"
        />
      </motion.div>

      {/* 分隔线 */}
      <div className="flex items-center justify-center mb-8">
        <div className="flex-1 h-px bg-gradient-to-r from-transparent via-ancient-400 to-transparent"></div>
        <div className="px-6 py-2 bg-ancient-200 rounded-full">
          <span className="text-ancient-700 font-chinese font-semibold">同时期对比</span>
        </div>
        <div className="flex-1 h-px bg-gradient-to-r from-transparent via-ancient-400 to-transparent"></div>
      </div>

      {/* 世界历史时间轴 */}
      <motion.div
        initial={{ opacity: 0, x: 50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
        className="mb-8"
      >
        <Timeline
          periods={timelineData.worldTimeline}
          viewport={viewport}
          onPeriodClick={handleWorldPeriodClick}
          onViewportChange={handleViewportChange}
          title="世界历史"
          className="world-timeline"
        />
      </motion.div>

      {/* 当前选中信息 */}
      {(selectedPeriods.china || selectedPeriods.world) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="selected-info bg-white rounded-lg shadow-lg p-6 border-2 border-imperial-200"
        >
          <h3 className="text-xl font-bold text-ancient-800 font-chinese mb-4 text-center">
            当前选中时期对比
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            {/* 中国历史信息 */}
            <div className="china-info">
              <h4 className="text-lg font-semibold text-ancient-700 font-chinese mb-2">
                中国历史
              </h4>
              {selectedPeriods.china ? (
                <div className="space-y-2">
                  <p className="font-semibold text-ancient-800">
                    {selectedPeriods.china.name}
                  </p>
                  <p className="text-sm text-ancient-600">
                    {selectedPeriods.china.description}
                  </p>
                  <p className="text-xs text-ancient-500">
                    政治制度：{selectedPeriods.china.politicalSystem}
                  </p>
                </div>
              ) : (
                <p className="text-ancient-500 italic">请点击中国历史时间轴选择时期</p>
              )}
            </div>

            {/* 世界历史信息 */}
            <div className="world-info">
              <h4 className="text-lg font-semibold text-ancient-700 font-chinese mb-2">
                世界历史
              </h4>
              {selectedPeriods.world ? (
                <div className="space-y-2">
                  <p className="font-semibold text-ancient-800">
                    {selectedPeriods.world.name}
                  </p>
                  <p className="text-sm text-ancient-600">
                    {selectedPeriods.world.description}
                  </p>
                  <p className="text-xs text-ancient-500">
                    政治制度：{selectedPeriods.world.politicalSystem}
                  </p>
                </div>
              ) : (
                <p className="text-ancient-500 italic">请点击世界历史时间轴选择时期</p>
              )}
            </div>
          </div>
        </motion.div>
      )}

      {/* 使用说明 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.8 }}
        className="instructions mt-8 bg-sage-50 rounded-lg p-4 border border-sage-200"
      >
        <h4 className="text-lg font-semibold text-sage-800 font-chinese mb-2">使用说明</h4>
        <ul className="text-sm text-sage-700 space-y-1 font-chinese">
          <li>• 鼠标拖拽时间轴可以左右移动查看不同时期</li>
          <li>• 滚轮可以缩放时间轴，查看更详细或更宏观的时间范围</li>
          <li>• 点击时间轴上的历史时期可以查看详细信息</li>
          <li>• 使用快速导航按钮可以快速跳转到重要历史时期</li>
          <li>• 两个时间轴同步显示，便于对比同时期的中外历史</li>
        </ul>
      </motion.div>
    </div>
  );
};
