import { HistoryPeriod } from '../types/history';

export const chinaHistoryData: HistoryPeriod[] = [
  {
    id: 'xia',
    name: '夏朝',
    startYear: -2070,
    endYear: -1600,
    dynasty: '夏',
    region: 'china',
    color: '#8B4513',
    description: '中国历史上第一个世袭制王朝，标志着中国进入奴隶社会。',
    capital: '阳城（今河南登封）',
    territory: '黄河中下游地区',
    politicalSystem: '世袭制王朝',
    keyEvents: [
      {
        id: 'yu-flood',
        year: -2123,
        title: '大禹治水',
        description: '大禹用疏导的方法治理洪水，获得成功',
        significance: '奠定了夏朝建立的基础',
        type: 'political',
        impact: 'regional'
      },
      {
        id: 'xia-establish',
        year: -2070,
        title: '夏朝建立',
        description: '禹的儿子启继承王位，建立夏朝',
        significance: '中国历史上第一个世袭制王朝',
        type: 'political',
        impact: 'regional'
      }
    ],
    culturalFeatures: ['青铜器初现', '文字雏形', '城市建设', '宗教祭祀'],
    economicFeatures: ['农业为主', '手工业发展', '青铜冶炼'],
    notablePersons: [
      {
        id: 'yu',
        name: '大禹',
        birthYear: -2200,
        deathYear: -2123,
        role: '夏朝开创者',
        achievements: ['治水成功', '建立夏朝'],
        significance: '中国历史上第一位王'
      }
    ],
    achievements: ['建立世袭制', '治水工程', '青铜技术']
  },
  {
    id: 'shang',
    name: '商朝',
    startYear: -1600,
    endYear: -1046,
    dynasty: '商',
    region: 'china',
    color: '#CD853F',
    description: '中国历史上第一个有确切文字记录的王朝，甲骨文的发现证实了商朝的存在。',
    capital: '亳（今河南商丘）、殷（今河南安阳）',
    territory: '黄河中下游，东至海滨，西至陕西，南至长江流域',
    politicalSystem: '神权与王权结合的奴隶制',
    keyEvents: [
      {
        id: 'tang-revolution',
        year: -1600,
        title: '商汤灭夏',
        description: '商汤在鸣条之战中击败夏桀，建立商朝',
        significance: '推翻暴政，建立新王朝',
        type: 'military',
        impact: 'regional'
      },
      {
        id: 'pan-geng-move',
        year: -1300,
        title: '盘庚迁殷',
        description: '商王盘庚将都城迁至殷（今安阳）',
        significance: '商朝进入鼎盛期',
        type: 'political',
        impact: 'regional'
      }
    ],
    culturalFeatures: ['甲骨文成熟', '青铜器鼎盛', '祭祀文化', '占卜传统'],
    economicFeatures: ['农业发达', '手工业繁荣', '商业贸易', '青铜冶炼技术'],
    notablePersons: [
      {
        id: 'tang',
        name: '商汤',
        birthYear: -1670,
        deathYear: -1587,
        role: '商朝开国君主',
        achievements: ['推翻夏朝', '建立商朝', '实行仁政'],
        significance: '商朝的建立者'
      }
    ],
    achievements: ['甲骨文字', '青铜文明', '历法制度', '城市规划']
  },
  {
    id: 'zhou',
    name: '西周',
    startYear: -1046,
    endYear: -771,
    dynasty: '周',
    region: 'china',
    color: '#A0522D',
    description: '中国历史上最长的王朝，建立了完善的封建制度和礼乐文明。',
    capital: '镐京（今陕西西安）',
    territory: '西起甘肃，东至山东，南达长江流域',
    politicalSystem: '分封制与宗法制',
    keyEvents: [
      {
        id: 'muye-battle',
        year: -1046,
        title: '牧野之战',
        description: '周武王在牧野击败商纣王，建立周朝',
        significance: '推翻商朝，建立周朝',
        type: 'military',
        impact: 'regional'
      },
      {
        id: 'fengjian-system',
        year: -1040,
        title: '分封制建立',
        description: '周王将土地和人民分封给诸侯',
        significance: '建立了完善的政治制度',
        type: 'political',
        impact: 'regional'
      }
    ],
    culturalFeatures: ['礼乐文明', '诗经文学', '易经哲学', '宗法观念'],
    economicFeatures: ['农业为本', '井田制', '手工业发展', '商业活动'],
    notablePersons: [
      {
        id: 'wuwang',
        name: '周武王',
        birthYear: -1087,
        deathYear: -1043,
        role: '西周开国君主',
        achievements: ['推翻商朝', '建立分封制', '制定礼乐'],
        significance: '西周王朝的建立者'
      }
    ],
    achievements: ['分封制度', '礼乐文明', '青铜工艺', '文字发展']
  },
  {
    id: 'dongzhou',
    name: '东周',
    startYear: -770,
    endYear: -256,
    dynasty: '周',
    region: 'china',
    color: '#8B4513',
    description: '周王室东迁后的时期，分为春秋和战国两个阶段，是中国思想文化的黄金时代。',
    capital: '洛邑（今河南洛阳）',
    territory: '名义上统治全国，实际控制力衰弱',
    politicalSystem: '分封制衰落，诸侯争霸',
    keyEvents: [
      {
        id: 'dongqian',
        year: -770,
        title: '平王东迁',
        description: '周平王迁都洛邑，东周开始',
        significance: '标志着西周结束，东周开始',
        type: 'political',
        impact: 'regional'
      },
      {
        id: 'chunqiu',
        year: -722,
        title: '春秋时代开始',
        description: '进入春秋争霸时代',
        significance: '诸侯争霸，礼崩乐坏',
        type: 'political',
        impact: 'regional'
      }
    ],
    culturalFeatures: ['诸子百家', '春秋五霸', '战国七雄', '儒家思想'],
    economicFeatures: ['铁器普及', '商业发展', '货币出现', '农业进步'],
    notablePersons: [
      {
        id: 'confucius',
        name: '孔子',
        birthYear: -551,
        deathYear: -479,
        role: '思想家、教育家',
        achievements: ['创立儒家学说', '编订六经', '有教无类'],
        significance: '中国古代最伟大的思想家之一'
      }
    ],
    achievements: ['诸子百家', '铁器时代', '货币制度', '思想解放']
  },
  {
    id: 'qin',
    name: '秦朝',
    startYear: -221,
    endYear: -206,
    dynasty: '秦',
    region: 'china',
    color: '#2F4F4F',
    description: '中国历史上第一个统一的中央集权制封建王朝，建立了影响深远的政治制度。',
    capital: '咸阳（今陕西咸阳）',
    territory: '东至海滨，西至陇西，南至南海，北至长城',
    politicalSystem: '中央集权制',
    keyEvents: [
      {
        id: 'unification',
        year: -221,
        title: '秦始皇统一六国',
        description: '秦王嬴政统一六国，建立秦朝',
        significance: '中国历史上第一次大统一',
        type: 'political',
        impact: 'regional'
      },
      {
        id: 'great-wall',
        year: -220,
        title: '修筑长城',
        description: '连接和修筑长城，抵御北方游牧民族',
        significance: '中国古代最伟大的工程之一',
        type: 'military',
        impact: 'regional'
      }
    ],
    culturalFeatures: ['统一文字', '焚书坑儒', '法家思想', '度量衡统一'],
    economicFeatures: ['统一货币', '修建驰道', '盐铁专卖', '重农抑商'],
    notablePersons: [
      {
        id: 'qinshihuang',
        name: '秦始皇',
        birthYear: -259,
        deathYear: -210,
        role: '秦朝开国皇帝',
        achievements: ['统一六国', '建立中央集权', '统一文字货币'],
        significance: '中国历史上第一位皇帝'
      }
    ],
    achievements: ['中央集权制', '统一文字', '万里长城', '兵马俑']
  }
];

// 导出函数获取指定年份的中国历史时期
export function getChinaPeriodByYear(year: number): HistoryPeriod | undefined {
  return chinaHistoryData.find(period => 
    year >= period.startYear && year <= period.endYear
  );
}

// 导出函数获取指定时间范围的中国历史时期
export function getChinaPeriodsInRange(startYear: number, endYear: number): HistoryPeriod[] {
  return chinaHistoryData.filter(period => 
    period.endYear >= startYear && period.startYear <= endYear
  );
}
