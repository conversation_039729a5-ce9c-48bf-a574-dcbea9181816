import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Keyboard, X } from 'lucide-react';

export const KeyboardShortcuts: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  const shortcuts = [
    { key: '← →', description: '左右移动时间轴' },
    { key: '+ =', description: '放大时间轴' },
    { key: '-', description: '缩小时间轴' },
    { key: '0', description: '重置缩放比例' },
    { key: 'ESC', description: '关闭弹窗' },
  ];

  return (
    <>
      {/* 快捷键按钮 */}
      <motion.button
        className="fixed bottom-6 right-6 bg-ancient-600 text-white p-3 rounded-full shadow-lg hover:bg-ancient-700 transition-colors z-40"
        onClick={() => setIsOpen(true)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title="键盘快捷键"
      >
        <Keyboard className="w-6 h-6" />
      </motion.button>

      {/* 快捷键面板 */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              className="fixed inset-0 bg-black bg-opacity-50 z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
            />

            {/* 快捷键内容 */}
            <motion.div
              className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-xl shadow-2xl p-6 max-w-md w-full mx-4 z-50"
              initial={{ opacity: 0, scale: 0.8, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: 20 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              {/* 标题栏 */}
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-ancient-800 font-chinese">
                  键盘快捷键
                </h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-ancient-500 hover:text-ancient-700 transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* 快捷键列表 */}
              <div className="space-y-4">
                {shortcuts.map((shortcut, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center justify-between py-3 px-4 bg-ancient-50 rounded-lg"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <span className="text-ancient-700 font-chinese">
                      {shortcut.description}
                    </span>
                    <div className="flex space-x-1">
                      {shortcut.key.split(' ').map((key, keyIndex) => (
                        <kbd
                          key={keyIndex}
                          className="px-2 py-1 bg-white border border-ancient-300 rounded text-sm font-mono text-ancient-800 shadow-sm"
                        >
                          {key}
                        </kbd>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* 鼠标操作说明 */}
              <div className="mt-6 pt-6 border-t border-ancient-200">
                <h4 className="text-lg font-semibold text-ancient-800 font-chinese mb-4">
                  鼠标操作
                </h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-ancient-700 font-chinese">拖拽移动</span>
                    <span className="text-sm text-ancient-500">鼠标拖拽</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-ancient-700 font-chinese">缩放时间轴</span>
                    <span className="text-sm text-ancient-500">滚轮</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-ancient-700 font-chinese">查看详情</span>
                    <span className="text-sm text-ancient-500">点击时期</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-ancient-700 font-chinese">悬停提示</span>
                    <span className="text-sm text-ancient-500">鼠标悬停</span>
                  </div>
                </div>
              </div>

              {/* 触摸操作说明 */}
              <div className="mt-6 pt-6 border-t border-ancient-200">
                <h4 className="text-lg font-semibold text-ancient-800 font-chinese mb-4">
                  触摸操作
                </h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-ancient-700 font-chinese">滑动移动</span>
                    <span className="text-sm text-ancient-500">单指滑动</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-ancient-700 font-chinese">缩放时间轴</span>
                    <span className="text-sm text-ancient-500">双指捏合</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-ancient-700 font-chinese">查看详情</span>
                    <span className="text-sm text-ancient-500">点击时期</span>
                  </div>
                </div>
              </div>

              {/* 提示 */}
              <div className="mt-6 p-4 bg-sage-50 rounded-lg border border-sage-200">
                <p className="text-sm text-sage-700 font-chinese text-center">
                  💡 提示：使用快速导航按钮可以快速跳转到重要历史时期
                </p>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};
