import React from 'react';
import { motion } from 'framer-motion';
import { SelectedPeriod, HistoryComparison } from '../types/history';
import { formatYear } from '../utils/timelineUtils';
import { Crown, Scroll, Users, Coins, MapPin, Calendar } from 'lucide-react';

interface HistoryDetailPanelProps {
  selectedPeriod: SelectedPeriod;
  comparison: HistoryComparison;
}

export const HistoryDetailPanel: React.FC<HistoryDetailPanelProps> = ({
  selectedPeriod,
  comparison
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="history-detail-panel bg-white rounded-xl shadow-2xl overflow-hidden border border-ancient-200"
    >
      {/* 面板标题 */}
      <div className="panel-header bg-gradient-to-r from-imperial-600 to-imperial-700 text-white p-6">
        <h2 className="text-2xl font-bold font-chinese text-center">
          {formatYear(comparison.year)} 历史详情对比
        </h2>
        <p className="text-imperial-100 text-center mt-2">
          探索这一时期中国与世界的历史发展
        </p>
      </div>

      {/* 主要内容区域 */}
      <div className="panel-content p-6">
        <div className="grid lg:grid-cols-2 gap-8">
          {/* 中国历史详情 */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="china-section"
          >
            <div className="section-header flex items-center mb-6">
              <div className="w-1 h-8 bg-imperial-500 rounded mr-4"></div>
              <h3 className="text-xl font-bold text-ancient-800 font-chinese">
                中国历史
              </h3>
            </div>

            {comparison.chinaContext.period.id ? (
              <div className="space-y-6">
                {/* 基本信息 */}
                <div className="basic-info bg-ancient-50 rounded-lg p-4">
                  <h4 className="font-semibold text-ancient-800 font-chinese mb-3 flex items-center">
                    <Crown className="w-5 h-5 mr-2 text-imperial-500" />
                    {comparison.chinaContext.period.name}
                  </h4>
                  <div className="grid grid-cols-1 gap-3 text-sm">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-2 text-ancient-600" />
                      <span className="text-ancient-700">
                        {formatYear(comparison.chinaContext.period.startYear)} - 
                        {formatYear(comparison.chinaContext.period.endYear)}
                      </span>
                    </div>
                    {comparison.chinaContext.period.capital && (
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 mr-2 text-ancient-600" />
                        <span className="text-ancient-700">
                          都城：{comparison.chinaContext.period.capital}
                        </span>
                      </div>
                    )}
                    <div className="flex items-center">
                      <Users className="w-4 h-4 mr-2 text-ancient-600" />
                      <span className="text-ancient-700">
                        政治制度：{comparison.chinaContext.period.politicalSystem}
                      </span>
                    </div>
                  </div>
                </div>

                {/* 历史描述 */}
                <div className="description">
                  <p className="text-ancient-700 leading-relaxed font-chinese">
                    {comparison.chinaContext.period.description}
                  </p>
                </div>

                {/* 文化特色 */}
                <div className="cultural-features">
                  <h5 className="font-semibold text-ancient-800 font-chinese mb-3 flex items-center">
                    <Scroll className="w-4 h-4 mr-2 text-sage-600" />
                    文化特色
                  </h5>
                  <div className="flex flex-wrap gap-2">
                    {comparison.chinaContext.period.culturalFeatures.map((feature, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-sage-100 text-sage-800 rounded-full text-sm font-chinese"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>

                {/* 经济特征 */}
                <div className="economic-features">
                  <h5 className="font-semibold text-ancient-800 font-chinese mb-3 flex items-center">
                    <Coins className="w-4 h-4 mr-2 text-imperial-600" />
                    经济特征
                  </h5>
                  <div className="flex flex-wrap gap-2">
                    {comparison.chinaContext.period.economicFeatures.map((feature, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-imperial-100 text-imperial-800 rounded-full text-sm font-chinese"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>

                {/* 重要事件 */}
                {comparison.chinaContext.events.length > 0 && (
                  <div className="key-events">
                    <h5 className="font-semibold text-ancient-800 font-chinese mb-3">
                      重要事件
                    </h5>
                    <div className="space-y-3">
                      {comparison.chinaContext.events.map((event) => (
                        <div key={event.id} className="event-item bg-ancient-50 rounded-lg p-3">
                          <div className="flex justify-between items-start mb-2">
                            <h6 className="font-semibold text-ancient-800 font-chinese">
                              {event.title}
                            </h6>
                            <span className="text-xs text-ancient-500">
                              {formatYear(event.year)}
                            </span>
                          </div>
                          <p className="text-sm text-ancient-700 font-chinese">
                            {event.description}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="no-data text-center py-8">
                <p className="text-ancient-500 font-chinese">
                  此时期中国尚未有明确的历史记录
                </p>
              </div>
            )}
          </motion.div>

          {/* 世界历史详情 */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="world-section"
          >
            <div className="section-header flex items-center mb-6">
              <div className="w-1 h-8 bg-sage-500 rounded mr-4"></div>
              <h3 className="text-xl font-bold text-ancient-800 font-chinese">
                世界历史
              </h3>
            </div>

            {comparison.worldContext.periods.length > 0 ? (
              <div className="space-y-6">
                {/* 同时期文明 */}
                <div className="civilizations">
                  <h5 className="font-semibold text-ancient-800 font-chinese mb-3">
                    同时期主要文明
                  </h5>
                  <div className="space-y-4">
                    {comparison.worldContext.periods.map((period) => (
                      <div key={period.id} className="civilization-item bg-sage-50 rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h6 className="font-semibold text-ancient-800 font-chinese">
                            {period.name}
                          </h6>
                          <span className="text-xs text-ancient-500">
                            {formatYear(period.startYear)} - {formatYear(period.endYear)}
                          </span>
                        </div>
                        <p className="text-sm text-ancient-700 font-chinese mb-3">
                          {period.description}
                        </p>
                        <div className="flex items-center text-sm text-ancient-600">
                          <MapPin className="w-3 h-3 mr-1" />
                          <span>{period.territory || '区域信息暂缺'}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 全球趋势 */}
                {comparison.worldContext.globalTrends.length > 0 && (
                  <div className="global-trends">
                    <h5 className="font-semibold text-ancient-800 font-chinese mb-3">
                      全球发展趋势
                    </h5>
                    <div className="flex flex-wrap gap-2">
                      {[...new Set(comparison.worldContext.globalTrends)].map((trend, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-sage-100 text-sage-800 rounded-full text-sm font-chinese"
                        >
                          {trend}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="no-data text-center py-8">
                <p className="text-ancient-500 font-chinese">
                  此时期世界其他地区的文明信息暂缺
                </p>
              </div>
            )}
          </motion.div>
        </div>

        {/* 历史联系分析 */}
        {comparison.connections.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="connections-section mt-8 pt-8 border-t border-ancient-200"
          >
            <h4 className="text-lg font-bold text-ancient-800 font-chinese mb-4 text-center">
              中外历史联系分析
            </h4>
            <div className="grid md:grid-cols-2 gap-4">
              {comparison.connections.map((connection, index) => (
                <div
                  key={index}
                  className="connection-item bg-gradient-to-r from-imperial-50 to-sage-50 rounded-lg p-4 border border-ancient-200"
                >
                  <p className="text-ancient-700 font-chinese text-sm leading-relaxed">
                    {connection}
                  </p>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};
