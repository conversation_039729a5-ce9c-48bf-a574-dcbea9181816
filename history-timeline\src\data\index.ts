import { TimelineData, HistoryComparison, HistoryPeriod } from '../types/history';
import { chinaHistoryData, getChinaPeriodByYear, getChinaPeriodsInRange } from './chinaHistory';
import { worldHistoryData, getWorldPeriodsByYear, getWorldPeriodsInRange } from './worldHistory';

// 主要时间轴数据
export const timelineData: TimelineData = {
  chinaTimeline: chinaHistoryData,
  worldTimeline: worldHistoryData,
  timeRange: {
    startYear: -3500, // 从最早的美索不达米亚文明开始
    endYear: 2024,    // 到现代
  }
};

// 获取指定年份的历史对比数据
export function getHistoryComparison(year: number): HistoryComparison | null {
  const chinaPeriod = getChinaPeriodByYear(year);
  const worldPeriods = getWorldPeriodsByYear(year);

  if (!chinaPeriod && worldPeriods.length === 0) {
    return null;
  }

  // 获取中国历史背景
  const chinaContext = chinaPeriod ? {
    period: chinaPeriod,
    events: chinaPeriod.keyEvents.filter(event => 
      Math.abs(event.year - year) <= 50 // 50年内的事件
    ),
    culturalContext: `${chinaPeriod.dynasty}时期，${chinaPeriod.description}`
  } : {
    period: {} as HistoryPeriod,
    events: [],
    culturalContext: '此时期中国尚未有明确的历史记录'
  };

  // 获取世界历史背景
  const worldContext = {
    periods: worldPeriods,
    events: worldPeriods.flatMap(period => 
      period.keyEvents.filter(event => Math.abs(event.year - year) <= 50)
    ),
    globalTrends: worldPeriods.flatMap(period => period.culturalFeatures)
  };

  // 分析中外历史联系
  const connections = analyzeHistoricalConnections(year, chinaPeriod, worldPeriods);

  return {
    year,
    chinaContext,
    worldContext,
    connections
  };
}

// 分析历史联系
function analyzeHistoricalConnections(
  year: number, 
  chinaPeriod: HistoryPeriod | undefined, 
  worldPeriods: HistoryPeriod[]
): string[] {
  const connections: string[] = [];

  if (!chinaPeriod) {
    return ['此时期中国文明尚未形成，世界其他地区已有文明发展'];
  }

  // 技术发展对比
  if (chinaPeriod.achievements.some(a => a.includes('青铜')) && 
      worldPeriods.some(p => p.achievements.some(a => a.includes('金属') || a.includes('青铜')))) {
    connections.push('中国与世界其他地区同期都在发展青铜技术');
  }

  // 文字系统对比
  if (chinaPeriod.culturalFeatures.some(f => f.includes('文字')) &&
      worldPeriods.some(p => p.culturalFeatures.some(f => f.includes('文字')))) {
    connections.push('文字系统的发明是人类文明发展的共同特征');
  }

  // 城市化进程对比
  if (chinaPeriod.culturalFeatures.some(f => f.includes('城市')) &&
      worldPeriods.some(p => p.culturalFeatures.some(f => f.includes('城市') || f.includes('城邦')))) {
    connections.push('城市化是古代文明发展的重要标志');
  }

  // 农业发展对比
  if (chinaPeriod.economicFeatures.some(f => f.includes('农业')) &&
      worldPeriods.some(p => p.economicFeatures.some(f => f.includes('农业')))) {
    connections.push('农业发展是各古代文明的经济基础');
  }

  // 贸易交流
  if (year > -2000 && chinaPeriod.economicFeatures.some(f => f.includes('贸易'))) {
    connections.push('通过丝绸之路等贸易路线，中国与世界其他地区开始有经济文化交流');
  }

  return connections;
}

// 获取时间范围内的所有历史时期
export function getPeriodsInTimeRange(startYear: number, endYear: number) {
  return {
    china: getChinaPeriodsInRange(startYear, endYear),
    world: getWorldPeriodsInRange(startYear, endYear)
  };
}

// 获取重要的历史节点年份（用于时间轴标记）
export function getImportantYears(): number[] {
  const importantYears = new Set<number>();

  // 添加中国历史重要年份
  chinaHistoryData.forEach(period => {
    importantYears.add(period.startYear);
    importantYears.add(period.endYear);
    period.keyEvents.forEach(event => {
      importantYears.add(event.year);
    });
  });

  // 添加世界历史重要年份
  worldHistoryData.forEach(period => {
    importantYears.add(period.startYear);
    importantYears.add(period.endYear);
    period.keyEvents.forEach(event => {
      importantYears.add(event.year);
    });
  });

  return Array.from(importantYears).sort((a, b) => a - b);
}

// 导出所有数据和函数
export {
  chinaHistoryData,
  worldHistoryData,
  getChinaPeriodByYear,
  getWorldPeriodsByYear,
  getChinaPeriodsInRange,
  getWorldPeriodsInRange
};
