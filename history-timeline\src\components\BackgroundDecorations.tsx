import React from 'react';
import { motion } from 'framer-motion';

export const BackgroundDecorations: React.FC = () => {
  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
      {/* 古典纹样背景 */}
      <div className="absolute inset-0 opacity-5">
        <svg
          className="w-full h-full"
          viewBox="0 0 1200 800"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* 中式云纹 */}
          <motion.path
            d="M100 200C150 150, 200 150, 250 200C300 250, 350 250, 400 200C450 150, 500 150, 550 200"
            stroke="currentColor"
            strokeWidth="2"
            fill="none"
            className="text-ancient-400"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{ duration: 3, delay: 0.5 }}
          />
          <motion.path
            d="M100 400C150 350, 200 350, 250 400C300 450, 350 450, 400 400C450 350, 500 350, 550 400"
            stroke="currentColor"
            strokeWidth="2"
            fill="none"
            className="text-ancient-400"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{ duration: 3, delay: 1 }}
          />
          <motion.path
            d="M100 600C150 550, 200 550, 250 600C300 650, 350 650, 400 600C450 550, 500 550, 550 600"
            stroke="currentColor"
            strokeWidth="2"
            fill="none"
            className="text-ancient-400"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{ duration: 3, delay: 1.5 }}
          />
          
          {/* 古典几何图案 */}
          <motion.circle
            cx="900"
            cy="150"
            r="50"
            stroke="currentColor"
            strokeWidth="1"
            fill="none"
            className="text-sage-300"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 2, delay: 2 }}
          />
          <motion.circle
            cx="900"
            cy="150"
            r="30"
            stroke="currentColor"
            strokeWidth="1"
            fill="none"
            className="text-sage-300"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 2, delay: 2.2 }}
          />
          <motion.circle
            cx="900"
            cy="150"
            r="10"
            fill="currentColor"
            className="text-sage-300"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 2, delay: 2.4 }}
          />
        </svg>
      </div>

      {/* 浮动的历史元素 */}
      <div className="absolute inset-0">
        {/* 古代钱币图案 */}
        <motion.div
          className="absolute top-20 left-10 w-16 h-16 rounded-full border-4 border-imperial-200 bg-imperial-50 opacity-20"
          animate={{
            y: [0, -20, 0],
            rotate: [0, 360],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <div className="w-full h-full flex items-center justify-center">
            <div className="w-6 h-6 border border-imperial-400 rounded-full"></div>
          </div>
        </motion.div>

        {/* 古代印章图案 */}
        <motion.div
          className="absolute top-40 right-20 w-12 h-12 bg-ancient-200 opacity-20 transform rotate-45"
          animate={{
            y: [0, 15, 0],
            x: [0, 10, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />

        {/* 古代卷轴图案 */}
        <motion.div
          className="absolute bottom-32 left-1/4 w-20 h-8 bg-ancient-100 opacity-20 rounded-full"
          animate={{
            y: [0, -10, 0],
            scaleX: [1, 1.1, 1],
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />

        {/* 古代鼎器图案 */}
        <motion.div
          className="absolute bottom-20 right-1/3 w-14 h-14 opacity-15"
          animate={{
            y: [0, -25, 0],
            rotate: [0, 5, -5, 0],
          }}
          transition={{
            duration: 9,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.5
          }}
        >
          <div className="w-full h-full bg-ancient-300 rounded-t-full relative">
            <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-2 h-4 bg-ancient-300 rounded-t"></div>
            <div className="absolute bottom-0 left-2 w-2 h-6 bg-ancient-300"></div>
            <div className="absolute bottom-0 right-2 w-2 h-6 bg-ancient-300"></div>
          </div>
        </motion.div>
      </div>

      {/* 光效装饰 */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-1/4 left-1/4 w-32 h-32 bg-gradient-radial from-imperial-200 to-transparent opacity-10 rounded-full"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.2, 0.1],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute top-3/4 right-1/4 w-24 h-24 bg-gradient-radial from-sage-200 to-transparent opacity-10 rounded-full"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.1, 0.15, 0.1],
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />
      </div>

      {/* 粒子效果 */}
      <div className="absolute inset-0">
        {Array.from({ length: 20 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-ancient-300 rounded-full opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -100, 0],
              opacity: [0, 0.6, 0],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: Math.random() * 2
            }}
          />
        ))}
      </div>
    </div>
  );
};
