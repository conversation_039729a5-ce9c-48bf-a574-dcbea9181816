// 简单的页面测试脚本
const puppeteer = require('puppeteer');

async function testPage() {
  console.log('🚀 开始测试历史时间轴应用...');
  
  let browser;
  try {
    // 启动浏览器
    browser = await puppeteer.launch({ 
      headless: false, // 设置为false可以看到浏览器
      defaultViewport: { width: 1200, height: 800 }
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
      console.log('📝 浏览器控制台:', msg.text());
    });
    
    // 监听错误
    page.on('error', err => {
      console.error('❌ 页面错误:', err.message);
    });
    
    console.log('🌐 正在访问 http://localhost:5173/');
    await page.goto('http://localhost:5173/', { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });
    
    // 等待加载动画完成
    console.log('⏳ 等待加载动画完成...');
    await page.waitForTimeout(2000);
    
    // 检查页面标题
    const title = await page.evaluate(() => {
      const titleElement = document.querySelector('h1');
      return titleElement ? titleElement.textContent : null;
    });
    
    if (title && title.includes('中华文明与世界文明对比时间轴')) {
      console.log('✅ 页面标题正确显示:', title);
    } else {
      console.log('❌ 页面标题未找到或不正确');
    }
    
    // 检查中国历史时间轴
    const chinaTimeline = await page.evaluate(() => {
      const elements = document.querySelectorAll('div');
      for (let el of elements) {
        if (el.textContent && el.textContent.includes('夏朝')) {
          return true;
        }
      }
      return false;
    });
    
    if (chinaTimeline) {
      console.log('✅ 中国历史时间轴正确显示');
    } else {
      console.log('❌ 中国历史时间轴未找到');
    }
    
    // 检查世界历史时间轴
    const worldTimeline = await page.evaluate(() => {
      const elements = document.querySelectorAll('div');
      for (let el of elements) {
        if (el.textContent && el.textContent.includes('美索不达米亚')) {
          return true;
        }
      }
      return false;
    });
    
    if (worldTimeline) {
      console.log('✅ 世界历史时间轴正确显示');
    } else {
      console.log('❌ 世界历史时间轴未找到');
    }
    
    // 检查页脚
    const footer = await page.evaluate(() => {
      const elements = document.querySelectorAll('*');
      for (let el of elements) {
        if (el.textContent && el.textContent.includes('专为中小学生历史教育设计')) {
          return true;
        }
      }
      return false;
    });
    
    if (footer) {
      console.log('✅ 页脚正确显示');
    } else {
      console.log('❌ 页脚未找到');
    }
    
    // 截图保存
    await page.screenshot({ 
      path: 'test-screenshot.png', 
      fullPage: true 
    });
    console.log('📸 页面截图已保存为 test-screenshot.png');
    
    console.log('🎉 测试完成！应用运行正常。');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testPage();
}

module.exports = testPage;
