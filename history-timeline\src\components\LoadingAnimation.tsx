import React from 'react';
import { motion } from 'framer-motion';

export const LoadingAnimation: React.FC = () => {
  return (
    <div className="fixed inset-0 bg-gradient-to-br from-ancient-50 via-ancient-100 to-sage-50 flex items-center justify-center z-50">
      <div className="text-center">
        {/* 主要加载动画 */}
        <div className="relative mb-8">
          {/* 外圈 - 代表时间的流逝 */}
          <motion.div
            className="w-32 h-32 border-4 border-ancient-200 rounded-full"
            animate={{ rotate: 360 }}
            transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
          />
          
          {/* 中圈 - 代表历史的传承 */}
          <motion.div
            className="absolute top-2 left-2 w-28 h-28 border-4 border-imperial-300 rounded-full border-t-transparent"
            animate={{ rotate: -360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          />
          
          {/* 内圈 - 代表文明的核心 */}
          <motion.div
            className="absolute top-4 left-4 w-24 h-24 border-4 border-sage-400 rounded-full border-r-transparent"
            animate={{ rotate: 360 }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
          />
          
          {/* 中心图标 - 古代印章样式 */}
          <motion.div
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-12 h-12 bg-gradient-to-br from-imperial-500 to-imperial-700 rounded-lg shadow-lg"
            animate={{
              scale: [1, 1.1, 1],
              boxShadow: [
                "0 4px 6px rgba(0,0,0,0.1)",
                "0 8px 25px rgba(220, 20, 60, 0.3)",
                "0 4px 6px rgba(0,0,0,0.1)"
              ]
            }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          >
            <div className="w-full h-full flex items-center justify-center text-white font-bold text-lg font-chinese">
              史
            </div>
          </motion.div>
        </div>

        {/* 加载文字 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="space-y-4"
        >
          <h2 className="text-2xl font-bold text-ancient-800 font-chinese">
            历史长河正在展开...
          </h2>
          
          {/* 动态加载提示 */}
          <motion.div
            className="flex justify-center space-x-1"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            {['正', '在', '加', '载', '历', '史', '数', '据'].map((char, index) => (
              <motion.span
                key={index}
                className="text-ancient-600 font-chinese"
                animate={{
                  opacity: [0.3, 1, 0.3],
                  y: [0, -5, 0]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: index * 0.1,
                  ease: "easeInOut"
                }}
              >
                {char}
              </motion.span>
            ))}
          </motion.div>

          {/* 进度条 */}
          <div className="w-64 h-2 bg-ancient-200 rounded-full overflow-hidden mx-auto">
            <motion.div
              className="h-full bg-gradient-to-r from-imperial-500 to-sage-500 rounded-full"
              initial={{ width: "0%" }}
              animate={{ width: "100%" }}
              transition={{ duration: 3, ease: "easeInOut" }}
            />
          </div>

          {/* 历史名言 */}
          <motion.p
            className="text-ancient-600 font-chinese text-sm italic max-w-md mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 1 }}
          >
            "以史为镜，可以知兴替" - 唐太宗
          </motion.p>
        </motion.div>

        {/* 装饰性历史元素 */}
        <div className="absolute inset-0 pointer-events-none">
          {/* 飘动的古代符号 */}
          {['龙', '凤', '鼎', '玉', '书', '画'].map((symbol, index) => (
            <motion.div
              key={symbol}
              className="absolute text-ancient-300 font-chinese text-lg opacity-20"
              style={{
                left: `${20 + index * 15}%`,
                top: `${30 + (index % 2) * 40}%`,
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0.1, 0.3, 0.1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{
                duration: 4 + index * 0.5,
                repeat: Infinity,
                ease: "easeInOut",
                delay: index * 0.3
              }}
            >
              {symbol}
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};
